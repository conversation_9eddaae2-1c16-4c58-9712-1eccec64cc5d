"""
Image management utilities for the Telegram bot.
Centralizes all image path resolution and image sending logic.
"""

import os
import logging
import asyncio
from typing import Optional, Union
from telegram import Message, Chat
from telegram.ext import ContextTypes
from telegram.error import TimedOut, NetworkError


class ImageManager:
    """Manages image paths and sending operations for the bot"""

    # Define image types and their corresponding file names with fallbacks
    IMAGE_TYPES = {
        'welcome': ['welcome.webp', 'welcome.png'],
        'membership_offer': ['membership_offer.webp'],
        'claim_membership': ['claim_membership.webp'],
        'verify_membership': ['verify_membership.webp'],
        'withdrawal': ['withdrawal.webp'],
        'support': ['support.webp']
    }

    def __init__(self, logger: Optional[logging.Logger] = None):
        """Initialize the image manager"""
        self.logger = logger or logging.getLogger(__name__)
        self.assets_dir = 'assets'
        self.fallback_assets_dir = 'telegram_bot/assets'

        # Timeout settings for image operations
        self.image_timeout = 15  # seconds for image operations
        self.text_timeout = 10   # seconds for text operations
        self.retry_attempts = 2  # number of retry attempts

    def get_image_path(self, image_type: str) -> Optional[str]:
        """
        Get the path to an image file if it exists.

        Args:
            image_type: Type of image (welcome, membership_offer, etc.)

        Returns:
            Path to the image file or None if not found
        """
        if image_type not in self.IMAGE_TYPES:
            self.logger.warning(f"Unknown image type: {image_type}")
            return None

        filenames = self.IMAGE_TYPES[image_type]
        if isinstance(filenames, str):
            filenames = [filenames]

        # Try each filename variant in order
        for filename in filenames:
            # Try primary assets directory first
            primary_path = os.path.join(self.assets_dir, filename)
            if os.path.exists(primary_path) and os.path.isfile(primary_path):
                self.logger.info(f"{image_type} image found at {primary_path}")
                return primary_path

            # Try fallback directory
            fallback_path = os.path.join(self.fallback_assets_dir, filename)
            if os.path.exists(fallback_path) and os.path.isfile(fallback_path):
                self.logger.info(f"{image_type} image found at {fallback_path}")
                return fallback_path

        self.logger.warning(f"{image_type} image not found in any location with filenames: {filenames}")
        return None

    async def send_message_with_image(
        self,
        message: Union[Message, Chat],
        text: str,
        image_type: str,
        reply_markup=None,
        context: Optional[ContextTypes.DEFAULT_TYPE] = None
    ) -> Optional[Message]:
        """
        Send a message with an image, falling back to text-only if image fails.

        Args:
            message: Message or Chat object to reply to
            text: Text content for the message
            image_type: Type of image to send
            reply_markup: Optional keyboard markup
            context: Bot context (required for Chat objects)

        Returns:
            Sent message object or None if failed
        """
        image_path = self.get_image_path(image_type)

        if image_path:
            return await self._send_with_image(message, text, image_path, reply_markup, context)
        else:
            return await self._send_text_only(message, text, reply_markup, context)

    async def _send_with_image(
        self,
        message: Union[Message, Chat],
        text: str,
        image_path: str,
        reply_markup=None,
        context: Optional[ContextTypes.DEFAULT_TYPE] = None
    ) -> Optional[Message]:
        """Send message with image, with timeout handling and retry logic"""
        self.logger.info(f"Sending message with image: {image_path}")

        for attempt in range(self.retry_attempts + 1):
            try:
                with open(image_path, 'rb') as photo:
                    self.logger.info(f"File opened successfully, sending photo (attempt {attempt + 1})...")

                    if hasattr(message, 'reply_photo'):
                        # Message object - use reply_photo
                        sent_message = await asyncio.wait_for(
                            message.reply_photo(
                                photo=photo,
                                caption=text,
                                reply_markup=reply_markup
                            ),
                            timeout=self.image_timeout
                        )
                    else:
                        # Chat object - use send_photo via context
                        if context and context.bot:
                            sent_message = await asyncio.wait_for(
                                context.bot.send_photo(
                                    chat_id=message.id,
                                    photo=photo,
                                    caption=text,
                                    reply_markup=reply_markup
                                ),
                                timeout=self.image_timeout
                            )
                        else:
                            self.logger.error("Cannot send photo: context.bot is not available")
                            return None

                    self.logger.info(f"Photo sent successfully (attempt {attempt + 1})")
                    return sent_message

            except (TimedOut, NetworkError, asyncio.TimeoutError) as e:
                if attempt < self.retry_attempts:
                    wait_time = (attempt + 1) * 3  # Exponential backoff: 3s, 6s, 9s
                    self.logger.warning(f"Image send timeout/network error (attempt {attempt + 1}), retrying in {wait_time}s: {e}")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    self.logger.error(f"Failed to send image after {self.retry_attempts + 1} attempts: {e}")
                    # Fall back to text message
                    return await self._send_text_only(message, text, reply_markup, context)

            except Exception as img_error:
                self.logger.error(f"Error sending image (attempt {attempt + 1}): {str(img_error)}")
                if attempt < self.retry_attempts:
                    await asyncio.sleep(2)
                    continue
                else:
                    # Fall back to text message
                    return await self._send_text_only(message, text, reply_markup, context)

        # Should not reach here, but fallback just in case
        return await self._send_text_only(message, text, reply_markup, context)

    async def _send_text_only(
        self,
        message: Union[Message, Chat],
        text: str,
        reply_markup=None,
        context: Optional[ContextTypes.DEFAULT_TYPE] = None
    ) -> Optional[Message]:
        """Send text-only message with timeout handling"""
        self.logger.info("Sending text-only message")

        for attempt in range(self.retry_attempts + 1):
            try:
                if hasattr(message, 'reply_text'):
                    # Message object - use reply_text
                    sent_message = await asyncio.wait_for(
                        message.reply_text(text, reply_markup=reply_markup),
                        timeout=self.text_timeout
                    )
                else:
                    # Chat object - use send_message via context
                    if context and context.bot:
                        sent_message = await asyncio.wait_for(
                            context.bot.send_message(
                                chat_id=message.id,
                                text=text,
                                reply_markup=reply_markup
                            ),
                            timeout=self.text_timeout
                        )
                    else:
                        self.logger.error("Cannot send message: context.bot is not available")
                        return None

                self.logger.info(f"Text message sent successfully (attempt {attempt + 1})")
                return sent_message

            except (TimedOut, NetworkError, asyncio.TimeoutError) as e:
                if attempt < self.retry_attempts:
                    wait_time = (attempt + 1) * 2  # Exponential backoff: 2s, 4s, 6s
                    self.logger.warning(f"Text send timeout/network error (attempt {attempt + 1}), retrying in {wait_time}s: {e}")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    self.logger.error(f"Failed to send text message after {self.retry_attempts + 1} attempts: {e}")
                    return None

            except Exception as text_error:
                self.logger.error(f"Error sending text message (attempt {attempt + 1}): {str(text_error)}")
                if attempt < self.retry_attempts:
                    await asyncio.sleep(1)
                    continue
                return None

        return None
