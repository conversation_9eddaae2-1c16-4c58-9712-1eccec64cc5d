from telegram import Update, Inline<PERSON><PERSON>boardButton, InlineKeyboardMarkup, ForceReply
from telegram.ext import ContextTypes
from telegram.error import TelegramError
from datetime import datetime, timezone
import traceback
import re
import os

# Import conversation states
from src.handlers.command_handlers import WAITING_FOR_CODE

# Import new utility classes
from src.utils.image_manager import ImageManager
from src.utils.message_helpers import MessageHelpers
from src.utils.bot_helpers import BotHelpers
from src.utils.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

# Add these constants at the top of the file
WAITING_FOR_EMAIL = 10  # Using different values from WAITING_FOR_CODE
WAITING_FOR_WHATSAPP = 11
WAITING_FOR_EXPERIENCE = 12

# New states for the account verification workflow
WAITING_FOR_ACCOUNT_NUMBER = 13
WAITING_FOR_NAME = 14
WAITING_FOR_VERIFICATION_EMAIL = 15
WAITING_FOR_VERIFICATION_WHATSAPP = 16

# State for the membership verification workflow
WAITING_FOR_VERIFY_ACCOUNT_NUMBER = 17

# States for the submit request workflow
WAITING_FOR_REQUEST_TEXT = 18
WAITING_FOR_REQUEST_NAME = 19
WAITING_FOR_REQUEST_EMAIL = 20
WAITING_FOR_REQUEST_WHATSAPP = 21

# States for the verification request workflow
WAITING_FOR_VERIFICATION_REQUEST_ACCOUNT = 22
WAITING_FOR_VERIFICATION_REQUEST_NAME = 23
WAITING_FOR_VERIFICATION_REQUEST_EMAIL = 24
WAITING_FOR_VERIFICATION_REQUEST_WHATSAPP = 25
WAITING_FOR_VERIFICATION_REQUEST_EXPERIENCE = 26

# State for the request workflow trading experience
WAITING_FOR_REQUEST_EXPERIENCE = 27

class MessageHandlers:
    """Handlers for text messages"""

    def __init__(self, config, subscription_manager):
        self.config = config
        self.logger = config.get_logger()
        self.sub_manager = subscription_manager

        # Initialize utility classes
        self.image_manager = ImageManager(self.logger)
        self.message_helpers = MessageHelpers(self.logger)
        self.bot_helpers = BotHelpers(config, self.logger)
        self.error_handler = ErrorHandler(self.logger)

        # We no longer store a static channel_link
        # Instead, we'll generate one-time use links when needed
        self.logger.info("MessageHandlers initialized with utility classes - will generate one-time use channel links when needed")

    def get_bot_id_safely(self, update):
        """Get the bot ID safely from an update"""
        return self.message_helpers.get_bot_id_safely(update)

    async def create_one_time_invite_link(self, context):
        """Create a one-time use invite link for the channel"""
        try:
            # Get chat_id from config
            chat_id_str = self.config.get('channel_id')
            if not chat_id_str:
                self.logger.error("Channel ID not found in configuration.")
                # Return a fallback link
                return self.config.get('channel_link', 'https://t.me/your_channel')

            # Format the chat_id properly for Telegram API
            # For channels and supergroups, the ID must start with -100
            # If it already has -100, keep it, otherwise add it
            if not chat_id_str.startswith('-100'):
                chat_id_str = f"-100{chat_id_str}"

            # Convert to integer
            chat_id = int(chat_id_str)
            self.logger.info(f"Attempting to create invite link for chat_id: {chat_id}")

            invite_link_obj = await context.bot.create_chat_invite_link(
                chat_id=chat_id,
                member_limit=1,  # CRUCIAL: Limits the link to exactly one use
                expire_date=None  # No expiration date, but it will expire after one use
            )
            self.logger.info(f"Created one-time invite link: {invite_link_obj.invite_link}")
            return invite_link_obj.invite_link

        except (TelegramError, ValueError, TypeError) as e:
            # Catch specific errors: Telegram issues, ValueError/TypeError if chat_id is invalid format
            self.logger.error(f"Error creating invite link for chat {chat_id_str if 'chat_id_str' in locals() else 'unknown'}: {e}")
            # Try with a different format as fallback
            try:
                if 'chat_id_str' in locals() and chat_id_str.startswith('-100'):
                    # Try without the -100 prefix
                    chat_id = int(chat_id_str.replace('-100', ''))
                    self.logger.info(f"Retrying with alternative chat_id format: {chat_id}")
                    invite_link_obj = await context.bot.create_chat_invite_link(
                        chat_id=chat_id,
                        member_limit=1,
                        expire_date=None
                    )
                    self.logger.info(f"Created one-time invite link with alternative format: {invite_link_obj.invite_link}")
                    return invite_link_obj.invite_link
            except Exception as retry_error:
                self.logger.error(f"Retry also failed: {retry_error}")

            # Return a fallback link
            return self.config.get('channel_link', 'https://t.me/your_channel')
        except Exception as e:
            self.logger.error(f"Unexpected error creating invite link: {e}")
            # Return a fallback link
            return self.config.get('channel_link', 'https://t.me/your_channel')

    def validate_email(self, email):
        """Validate email format"""
        is_valid, _ = self.message_helpers.validate_text_input(email, min_length=5, max_length=254)
        if not is_valid:
            return False
        # Basic email validation
        return '@' in email and '.' in email.split('@')[-1]

    def validate_whatsapp(self, number):
        """Validate WhatsApp number format (basic check for + and digits)"""
        # Simple validation for international format: +CountryCodeNumber
        return bool(re.match(r'^\+[0-9]{1,4}[0-9]{6,14}$', number.replace(' ', '').replace('-', '').replace('(', '').replace(')', '')))

    # Remove these duplicate state definitions and use the imported ones
    # WAITING_FOR_CODE = 0
    # WAITING_FOR_EMAIL = 1
    # WAITING_FOR_WHATSAPP = 2
    # WAITING_FOR_EXPERIENCE = 3

    async def check_code(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Validate access code before collecting user details"""
        user_id = update.message.from_user.id
        access_code = update.message.text.strip()

        # Store the bot ID in context for later use
        telegram_bot_id = self.get_bot_id_safely(update)
        if telegram_bot_id:
            context.user_data['telegram_bot_id'] = telegram_bot_id
            self.logger.info(f"Stored telegram_bot_id in context for user {user_id}: {telegram_bot_id}")

        # First check if the code exists in the access_code collection
        is_valid_in_access_codes = self.sub_manager.get_tenant_database(context).verify_access_code(access_code)
        self.logger.info(f"Access code exists in access_code collection: {is_valid_in_access_codes}")

        # Check if the code is already used by another user
        existing_users = self.sub_manager.get_users_by_code(access_code, context)
        code_used_by_other = existing_users and str(user_id) not in [str(u) for u in existing_users]
        self.logger.info(f"Code used by other check: {code_used_by_other}, existing users: {existing_users}")

        # Determine if the access code is valid for this user
        is_valid = is_valid_in_access_codes and not code_used_by_other
        self.logger.info(f"Access code is valid for user {user_id}: {is_valid}")

        # Get user data to check if this is an expired user trying to use their old code
        user_data = self.sub_manager.get_user_data(user_id, context)
        if user_data and (user_data.get('user_status') == 'expired' or user_data.get('subscription') == 'expired') and user_data.get('access_code') == access_code:
            # This is an expired user trying to use their old code
            message_text = "This access code is not valid. Your subscription has expired."
            keyboard = [
                [InlineKeyboardButton("Retry Verification ✅", callback_data='reg_start_verification')],
                [InlineKeyboardButton("Apply for Membership 🏆", callback_data='verify_apply_new')],
                [InlineKeyboardButton("Submit a Manual verification Request 🔍", callback_data='verification_request_start')],
                [InlineKeyboardButton("Change your Partner/IB 🥷", callback_data='partner_change_start')],
                [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
            ]

            await update.message.reply_text(
                message_text,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
            return WAITING_FOR_CODE
        elif not is_valid:
            await update.message.reply_text("❌ Invalid access code. Please try again:")
            return WAITING_FOR_CODE

        if existing_users and str(user_id) not in [str(u) for u in existing_users]:
            # The code is already used by another user
            self.logger.warning(f"Access code {access_code} is already used by another user. User ID: {user_id}")

            # Show a clear error message about the code being already in use
            message_text = "❌ This access code is already associated with another user and cannot be used again. Please enter a different access code."

            # Get the account number image path
            account_number_image_path = os.path.join('assets', 'account_number_image.png')

            # Check if the image exists
            if os.path.exists(account_number_image_path) and os.path.isfile(account_number_image_path):
                self.logger.info(f"Account number image found at {account_number_image_path}")
                try:
                    # First send the error message
                    await update.message.reply_text(message_text)

                    # Then send the image with the prompt
                    with open(account_number_image_path, 'rb') as photo:
                        await update.message.reply_photo(
                            photo=photo,
                            caption="Enter the account number that is available in your trading profile:",
                            reply_markup=ForceReply(selective=True)
                        )
                except Exception as e:
                    self.logger.error(f"Error sending account number image: {str(e)}")
                    # Fall back to text message if image fails
                    await update.message.reply_text(
                        f"{message_text}\n\nEnter the account number that is available in your trading profile:",
                        reply_markup=ForceReply(selective=True)
                    )
            else:
                # If image doesn't exist, just send text
                self.logger.warning(f"Account number image not found at {account_number_image_path}")
                await update.message.reply_text(
                    f"{message_text}\n\nEnter the account number that is available in your trading profile:",
                    reply_markup=ForceReply(selective=True)
                )

            # Don't end the conversation here
            return WAITING_FOR_CODE

        # Code is valid and available - store it and proceed
        context.user_data['access_code'] = access_code
        self.logger.info(f"User {user_id} provided valid access code: {access_code}")

        # Get user's name from Telegram profile
        first_name = update.message.from_user.first_name or ""
        last_name = update.message.from_user.last_name or ""
        context.user_data['name'] = f"{first_name} {last_name}".strip() or "Not Provided"

        # Proceed to collect email
        await update.message.reply_text(
            "📧 Please enter your email address:",
            reply_markup=ForceReply(selective=True)
        )
        return WAITING_FOR_EMAIL

    async def handle_email(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle email input"""
        email = update.message.text.strip()

        if not self.validate_email(email):
            await update.message.reply_text(
                "⚠️ Invalid email format. Please enter a valid email address:",
                reply_markup=ForceReply(selective=True)
            )
            return WAITING_FOR_EMAIL

        context.user_data['email'] = email
        self.logger.info(f"User {update.message.from_user.id} provided email: {email}")

        await update.message.reply_text(
            "📱 Please provide your WhatsApp number with country code (e.g., +1XXXXXXXXXX):",
            reply_markup=ForceReply(selective=True)
        )
        return WAITING_FOR_WHATSAPP

    @staticmethod
    def validate_email(email: str) -> bool:
        """Basic email validation"""
        if '@' not in email or '.' not in email.split('@')[-1]:
            return False
        if len(email) > 254:
            return False
        return True

    @staticmethod
    def validate_whatsapp(number: str) -> bool:
        """Validate international WhatsApp numbers in E.164 format"""
        # Remove any spaces or formatting characters
        clean_number = number.replace(' ', '').replace('-', '').replace('(', '').replace(')', '')

        # Check if it starts with + and has at least 7 digits (smallest valid number length)
        if clean_number.startswith('+'):
            # Remove the + and check if the rest is numeric and reasonable length
            num_part = clean_number[1:]
            return num_part.isdigit() and 7 <= len(num_part) <= 15

        # If no +, it's invalid - we require country code
        return False

    def get_bot_id_safely(self, update):
        """Get the bot ID safely from the update object"""
        try:
            if hasattr(update, 'message') and update.message and hasattr(update.message, 'bot'):
                return update.message.bot.id
            elif hasattr(update, 'callback_query') and update.callback_query and hasattr(update.callback_query.message, 'bot'):
                return update.callback_query.message.bot.id
            else:
                self.logger.warning("Could not find bot ID in update object")
                return None
        except Exception as e:
            self.logger.error(f"Error getting bot ID: {str(e)}")
            return None

    async def handle_whatsapp(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle WhatsApp number input"""
        number = update.message.text.strip()

        if not self.validate_whatsapp(number):
            await update.message.reply_text(
                "⚠️ Please enter a valid phone number with country code (e.g., +1XXXXXXXXXX for US, +44XXXXXXXXX for UK):",
                reply_markup=ForceReply(selective=True)
            )
            return WAITING_FOR_WHATSAPP

        # Store the number as provided (with the + sign)
        formatted_number = number.replace(' ', '').replace('-', '').replace('(', '').replace(')', '')

        context.user_data['whatsapp'] = formatted_number
        self.logger.info(f"User {update.message.from_user.id} provided WhatsApp: {formatted_number}")

        # Ask for trading experience
        keyboard = [
            [InlineKeyboardButton("Beginner", callback_data="beginner")],
            [InlineKeyboardButton("Intermediate", callback_data="intermediate")],
            [InlineKeyboardButton("Pro", callback_data="pro")]
        ]

        await update.message.reply_text(
            "Please select your trading experience level:",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
        return WAITING_FOR_EXPERIENCE

    # New handlers for the account verification workflow

    async def check_account_number(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Validate account number and check if it exists in the database"""
        try:
            user_id = update.message.from_user.id
            account_number = update.message.text.strip()

            self.logger.info(f"Received account number from user {user_id}: {account_number}")

            # Store the account number in context for later use
            context.user_data['account_number'] = account_number
            self.logger.info(f"Stored account number in context for user {user_id}: {account_number}")

            # First check if the code exists in the access_code collection
            is_valid_in_access_codes = self.sub_manager.db.verify_access_code(account_number)
            self.logger.info(f"Access code exists in access_code collection: {is_valid_in_access_codes}")

            # Check if the account is already used by another user
            existing_users = self.sub_manager.get_users_by_code(account_number)
            account_used_by_other = existing_users and str(user_id) not in [str(u) for u in existing_users]
            self.logger.info(f"Account used by other check: {account_used_by_other}, existing users: {existing_users}")

            # Check if this is an expired user trying to use their old code
            user_data = self.sub_manager.get_user_data(user_id)
            is_expired_user = user_data and (user_data.get('user_status') == 'expired' or user_data.get('subscription') == 'expired') and user_data.get('access_code') == account_number
            self.logger.info(f"Is expired user check: {is_expired_user}")

            # Determine if the account exists and is valid for this user
            account_exists = is_valid_in_access_codes and not account_used_by_other and not is_expired_user
            self.logger.info(f"Account exists and is valid for user {user_id}: {account_exists}")

            if is_expired_user:
                # This is an expired user trying to use their old code
                message_text = "This access code is not valid. Your subscription has expired."
                keyboard = [
                    [InlineKeyboardButton("Retry Verification ✅", callback_data='reg_start_verification')],
                    [InlineKeyboardButton("Apply for Membership 🏆", callback_data='verify_apply_new')],
                    [InlineKeyboardButton("Submit a Manual verification Request 🔍", callback_data='verification_request_start')],
                    [InlineKeyboardButton("Change your Partner/IB 🥷", callback_data='partner_change_start')],
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ]

                await update.message.reply_text(
                    message_text,
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )
                return WAITING_FOR_CODE
            elif not is_valid_in_access_codes:
                # Account not found - show options
                message_text = "Sorry, We could not find your account under our IB ⚠️"
                keyboard = [
                    [InlineKeyboardButton("Submit a Manual verification Request 🔁", callback_data='verification_request_start')],
                    [InlineKeyboardButton("Notify Me once my account is linked 🔔", callback_data='notify_account_linked')],
                    [InlineKeyboardButton("Change IB and ReApply ☕", callback_data='partner_change_start')],
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ]

                await update.message.reply_text(
                    message_text,
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )
                return WAITING_FOR_ACCOUNT_NUMBER
            elif account_used_by_other:
                # Account is already used by another user
                self.logger.warning(f"Account number {account_number} is already used by another user. User ID: {user_id}")

                # Show a clear error message about the account being already in use
                message_text = "❌ This account number is already associated with another user and cannot be used again. Please enter a different account number."

                # Get the account number image path
                account_number_image_path = os.path.join('assets', 'account_number_image.png')

                # Check if the image exists
                if os.path.exists(account_number_image_path) and os.path.isfile(account_number_image_path):
                    self.logger.info(f"Account number image found at {account_number_image_path}")
                    try:
                        # First send the error message
                        await update.message.reply_text(message_text)

                        # Then send the image with the prompt
                        with open(account_number_image_path, 'rb') as photo:
                            await update.message.reply_photo(
                                photo=photo,
                                caption="Enter the account number that is available in your trading profile:",
                                reply_markup=ForceReply(selective=True)
                            )
                    except Exception as e:
                        self.logger.error(f"Error sending account number image: {str(e)}")
                        # Fall back to text message if image fails
                        await update.message.reply_text(
                            f"{message_text}\n\nEnter the account number that is available in your trading profile:",
                            reply_markup=ForceReply(selective=True)
                        )
                else:
                    # If image doesn't exist, just send text
                    self.logger.warning(f"Account number image not found at {account_number_image_path}")
                    await update.message.reply_text(
                        f"{message_text}\n\nEnter the account number that is available in your trading profile:",
                        reply_markup=ForceReply(selective=True)
                    )

                return WAITING_FOR_ACCOUNT_NUMBER
            else:
                # Account found - check if user already exists in master_user_data
                user_data = self.sub_manager.get_master_user_data(user_id)

                if user_data and all(key in user_data for key in ['name', 'email', 'whatsapp', 'trading_experience']):
                    # User already exists with complete data - skip data collection
                    self.logger.info(f"User {user_id} already exists in master_user_data with complete information")

                    # Store the existing data in context for later use
                    context.user_data['verification_name'] = user_data.get('name')
                    context.user_data['verification_email'] = user_data.get('email')
                    context.user_data['verification_whatsapp'] = user_data.get('whatsapp')
                    context.user_data['verification_trading_experience'] = user_data.get('trading_experience')

                    # Send confirmation message
                    message_text = f"Welcome back {user_data.get('name')}! We already have your details.\n\nEmail: {user_data.get('email')}\nWhatsApp: {user_data.get('whatsapp')}\nTrading Experience: {user_data.get('trading_experience')}"

                    keyboard = [
                        [InlineKeyboardButton("Proceed with these details", callback_data='verification_use_existing')],
                        [InlineKeyboardButton("Update my details", callback_data='verification_update_details')],
                        [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                    ]

                    await update.message.reply_text(
                        message_text,
                        reply_markup=InlineKeyboardMarkup(keyboard)
                    )
                    return WAITING_FOR_CODE
                else:
                    # User doesn't exist or has incomplete data - proceed to collect information
                    # Ask for name
                    await update.message.reply_text(
                        "👤Your Good Name?",
                        reply_markup=ForceReply(selective=True)
                    )
                    return WAITING_FOR_NAME
        except Exception as e:
            self.logger.error(f"Error in check_account_number: {str(e)}")
            traceback.print_exc()
            await update.message.reply_text("Sorry, there was an error processing your request. Please try again later.")
            return WAITING_FOR_CODE

    async def handle_verification_name(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle name input for verification"""
        try:
            user_id = update.message.from_user.id
            name = update.message.text.strip()

            self.logger.info(f"Received name from user {user_id}: {name}")

            # Store the name in context
            context.user_data['verification_name'] = name
            self.logger.info(f"Stored name in context for user {user_id}: {name}")

            # Ask for email
            await update.message.reply_text(
                "📧Your Email Address?",
                reply_markup=ForceReply(selective=True)
            )
            return WAITING_FOR_VERIFICATION_EMAIL
        except Exception as e:
            self.logger.error(f"Error in handle_verification_name: {str(e)}")
            traceback.print_exc()
            await update.message.reply_text("Sorry, there was an error processing your request. Please try again later.")
            return WAITING_FOR_CODE

    async def handle_verification_email(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle email input for verification"""
        email = update.message.text.strip()

        if not self.validate_email(email):
            await update.message.reply_text(
                "⚠️ Invalid email format. Please enter a valid email address:",
                reply_markup=ForceReply(selective=True)
            )
            return WAITING_FOR_VERIFICATION_EMAIL

        # Store the email in context
        context.user_data['verification_email'] = email
        self.logger.info(f"User {update.message.from_user.id} provided email: {email}")

        # Ask for WhatsApp number
        await update.message.reply_text(
            "📞Your Whatsapp number with country code (e.g., +91 9876543210) ?",
            reply_markup=ForceReply(selective=True)
        )
        return WAITING_FOR_VERIFICATION_WHATSAPP

    async def handle_verification_whatsapp(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle WhatsApp number input for verification"""
        try:
            user_id = update.message.from_user.id
            number = update.message.text.strip()

            # Validate WhatsApp number format
            if not self.validate_whatsapp(number):
                await update.message.reply_text(
                    "⚠️ Please enter a valid phone number with country code (e.g., +1XXXXXXXXXX for US, +44XXXXXXXXX for UK):",
                    reply_markup=ForceReply(selective=True)
                )
                return WAITING_FOR_VERIFICATION_WHATSAPP

            # Format and store the WhatsApp number
            formatted_number = number.replace(' ', '').replace('-', '').replace('(', '').replace(')', '')
            context.user_data['verification_whatsapp'] = formatted_number
            self.logger.info(f"User {user_id} provided WhatsApp: {formatted_number}")

            # Ask for trading experience
            keyboard = [
                [InlineKeyboardButton("Beginner", callback_data="beginner_verification")],
                [InlineKeyboardButton("Intermediate", callback_data="intermediate_verification")],
                [InlineKeyboardButton("Pro", callback_data="pro_verification")]
            ]

            await update.message.reply_text(
                "Please select your trading experience level:",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
            return WAITING_FOR_EXPERIENCE
        except Exception as e:
            self.logger.error(f"Error in handle_verification_whatsapp: {str(e)}")
            traceback.print_exc()
            await update.message.reply_text("Sorry, there was an error processing your request. Please try again later.")
            return WAITING_FOR_CODE

    async def check_verify_account_number(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Check if the provided account number exists in the database"""
        user_id = update.message.from_user.id
        account_number = update.message.text.strip()

        self.logger.info(f"User {user_id} is verifying account number: {account_number}")

        # First, check if the user is already verified
        is_verified, status, status_message = self.sub_manager.check_subscription_status_and_get_message(user_id)
        self.logger.info(f"Subscription check result for user {user_id}: is_verified={is_verified}, status={status}, status_message={status_message}")

        user_data = self.sub_manager.get_user_data(user_id)

        if user_data and is_verified and status == 'active':
            # User is already verified and has an active subscription
            user_name = user_data.get('name', 'there')
            message_text = f"Welcome back {user_name}! Your profile is verified ✅ You are an exclusive Member With us 🥷"

            # Create a one-time use invite link
            channel_link = await self.create_one_time_invite_link(context)

            keyboard = [
                [InlineKeyboardButton("Access Premium space 🏆", url=channel_link)],
                [InlineKeyboardButton("Explore my Benefits 🌍", callback_data="verify_explore_offerings")],
                [InlineKeyboardButton("My Community Ranking 🏅", callback_data="community_ranking")],
                [InlineKeyboardButton("Contact Withdrawals/Deposit Team 🔰", url="http://wa.me/+************")],
                [InlineKeyboardButton("Need More Support?", callback_data="submit_request_start")],
                [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
            ]

            await update.message.reply_text(
                message_text,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

            return WAITING_FOR_CODE

        # Store the account number in context for later use
        context.user_data['verify_account_number'] = account_number

        # First check if the code exists in the access_code collection
        is_valid_in_access_codes = self.sub_manager.db.verify_access_code(account_number)
        self.logger.info(f"Access code exists in access_code collection: {is_valid_in_access_codes}")

        # Check if the code is already used by another user
        existing_users = self.sub_manager.get_users_by_code(account_number)
        code_used_by_other = existing_users and str(user_id) not in [str(u) for u in existing_users]
        self.logger.info(f"Code used by other check: {code_used_by_other}, existing users: {existing_users}")

        # Determine if the access code is valid for this user
        access_code_valid = is_valid_in_access_codes and not code_used_by_other
        self.logger.info(f"Access code is valid for user {user_id}: {access_code_valid}")

        # Get user data to check if this is an expired user trying to use their old code
        user_data = self.sub_manager.get_user_data(user_id)
        if user_data and (user_data.get('user_status') == 'expired' or user_data.get('subscription') == 'expired') and user_data.get('access_code') == account_number:
            # This is an expired user trying to use their old code
            message_text = "This access code is not valid. Your subscription has expired."
            keyboard = [
                [InlineKeyboardButton("Retry Verification ✅", callback_data='reg_start_verification')],
                [InlineKeyboardButton("Apply for Membership 🏆", callback_data='verify_apply_new')],
                [InlineKeyboardButton("Submit a Manual verification Request 🔍", callback_data='verification_request_start')],
                [InlineKeyboardButton("Change your Partner/IB 🥷", callback_data='partner_change_start')],
                [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
            ]

            await update.message.reply_text(
                message_text,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
            return WAITING_FOR_CODE
        elif code_used_by_other:
            # The code is already used by another user
            self.logger.warning(f"Access code {account_number} is already used by another user. User ID: {user_id}")

            # Show a clear error message about the account being already in use
            message_text = "❌ This account number is already associated with another user and cannot be used again. Please enter a different account number."

            # Get the account number image path
            account_number_image_path = os.path.join('assets', 'account_number_image.png')

            # Check if the image exists
            if os.path.exists(account_number_image_path) and os.path.isfile(account_number_image_path):
                self.logger.info(f"Account number image found at {account_number_image_path}")
                try:
                    # First send the error message
                    await update.message.reply_text(message_text)

                    # Then send the image with the prompt
                    with open(account_number_image_path, 'rb') as photo:
                        await update.message.reply_photo(
                            photo=photo,
                            caption="Enter the account number that is available in your trading profile:",
                            reply_markup=ForceReply(selective=True)
                        )
                except Exception as e:
                    self.logger.error(f"Error sending account number image: {str(e)}")
                    # Fall back to text message if image fails
                    await update.message.reply_text(
                        f"{message_text}\n\nEnter the account number that is available in your trading profile:",
                        reply_markup=ForceReply(selective=True)
                    )
            else:
                # If image doesn't exist, just send text
                self.logger.warning(f"Account number image not found at {account_number_image_path}")
                await update.message.reply_text(
                    f"{message_text}\n\nEnter the account number that is available in your trading profile:",
                    reply_markup=ForceReply(selective=True)
                )

            # Don't end the conversation here
            return WAITING_FOR_VERIFY_ACCOUNT_NUMBER
        elif access_code_valid:
            # The access code is valid, now check if it's associated with any user
            existing_users = self.sub_manager.get_users_by_code(account_number)

            # Check if the current user is one of the users associated with this code
            user_has_this_code = existing_users and str(user_id) in [str(u) for u in existing_users]

            if user_has_this_code:
                # User already has this access code - show success message
                message_text = "Looks great! You're already registered with us 🥷\n\nThis account is registered under your user ID."
                keyboard = [
                    [InlineKeyboardButton("Claim your Membership 🏆", callback_data='verify_claim_existing')],
                    [InlineKeyboardButton("Explore our Offerings 🎉", callback_data='verify_explore_offerings')],
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ]

                await update.message.reply_text(
                    message_text,
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )
            else:
                # Access code is valid but not associated with this user - collect user details first
                self.logger.info(f"Valid access code {account_number} provided by user {user_id} - collecting user details")

                # Store the access code in context for later use
                context.user_data['verification_access_code'] = account_number

                # Ask for name
                await update.message.reply_text(
                    "👤Your Good Name?",
                    reply_markup=ForceReply(selective=True)
                )
                return WAITING_FOR_VERIFICATION_REQUEST_NAME
        else:
            # Account not found in our system - show options
            message_text = "Sorry we could not find your registration with us 💀"
            keyboard = [
                [InlineKeyboardButton("Apply for Membership 🏆", callback_data='verify_apply_new')],
                [InlineKeyboardButton("Submit a Manual verification Request 🔍", callback_data='verification_request_start')],
                [InlineKeyboardButton("Change your Partner/IB 🥷", callback_data='partner_change_start')],
                [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
            ]

            await update.message.reply_text(
                message_text,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

        # Return to waiting for code state to handle button clicks
        return WAITING_FOR_CODE

    # Handlers for the submit request workflow

    async def handle_request_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle the initial request text from the user"""
        user_id = update.message.from_user.id
        request_text = update.message.text.strip()

        self.logger.info(f"Handling request text from user {user_id}: {request_text}")
        self.logger.info(f"Current context.user_data: {context.user_data}")

        try:
            # Check if we're adding to existing request text and request_id
            if 'request_id' in context.user_data and 'request_text' in context.user_data:
                # Add the new text as an additional message to the existing support request
                request_id = context.user_data['request_id']
                self.logger.info(f"Adding to existing request {request_id} for user {user_id}")

                try:
                    success = self.sub_manager.add_message_to_support_request(request_id, request_text)
                    if success:
                        self.logger.info(f"Added additional message to support request {request_id} for user {user_id}")
                        # Update the request text in context for display purposes
                        context.user_data['request_text'] += "\n\nAdditional details: " + request_text
                    else:
                        self.logger.error(f"Failed to add message to support request {request_id} for user {user_id}")
                except Exception as e:
                    self.logger.error(f"Error adding message to support request: {str(e)}")
            else:
                # Store the request text in context
                context.user_data['request_text'] = request_text
                self.logger.info(f"Creating new support request for user {user_id}")

                try:
                    # Create a new support request in the database
                    # Get the bot ID
                    telegram_bot_id = self.get_bot_id_safely(update)
                    request_id, success = self.sub_manager.create_support_request(user_id, request_text, telegram_bot_id=telegram_bot_id)
                    if success:
                        self.logger.info(f"Created new support request {request_id} for user {user_id} with bot ID {telegram_bot_id}")
                        # Store the request_id in context for future reference
                        context.user_data['request_id'] = request_id
                    else:
                        self.logger.error(f"Failed to create support request for user {user_id}")
                except Exception as e:
                    self.logger.error(f"Error creating support request: {str(e)}")

            self.logger.info(f"User {user_id} submitted request text: {request_text}")
            self.logger.info(f"Updated context.user_data: {context.user_data}")
        except Exception as e:
            self.logger.error(f"Unexpected error in handle_request_text: {str(e)}")
            traceback.print_exc()

        # Ask if the user wants to add more details or submit the request
        message_text = "Your request has been recorded, would you like to :"
        keyboard = [
            [InlineKeyboardButton("Add more Details? ℹ️", callback_data='support_add_details')],
            [InlineKeyboardButton("Submit the request 🏁", callback_data='support_finalize_request')],
            [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
        ]

        await update.message.reply_text(
            message_text,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

        return WAITING_FOR_CODE

    async def handle_request_name(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle name input for request submission"""
        name = update.message.text.strip()

        # Store the name in context
        context.user_data['request_name'] = name
        self.logger.info(f"User {update.message.from_user.id} provided name: {name}")

        # Ask for email
        await update.message.reply_text(
            "📧Your Email Address?",
            reply_markup=ForceReply(selective=True)
        )
        return WAITING_FOR_REQUEST_EMAIL

    async def handle_request_email(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle email input for request submission"""
        email = update.message.text.strip()

        if not self.validate_email(email):
            await update.message.reply_text(
                "⚠️ Invalid email format. Please enter a valid email address:",
                reply_markup=ForceReply(selective=True)
            )
            return WAITING_FOR_REQUEST_EMAIL

        # Store the email in context
        context.user_data['request_email'] = email
        self.logger.info(f"User {update.message.from_user.id} provided email: {email}")

        # Ask for WhatsApp number
        await update.message.reply_text(
            "📞Your Whatsapp number with country code (e.g., +91 9876543210) ?",
            reply_markup=ForceReply(selective=True)
        )
        return WAITING_FOR_REQUEST_WHATSAPP

    async def handle_request_whatsapp(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle WhatsApp number input for request submission"""
        try:
            user_id = update.message.from_user.id
            number = update.message.text.strip()

            if not self.validate_whatsapp(number):
                await update.message.reply_text(
                    "⚠️ Please enter a valid phone number with country code (e.g., +1XXXXXXXXXX for US, +44XXXXXXXXX for UK):",
                    reply_markup=ForceReply(selective=True)
                )
                return WAITING_FOR_REQUEST_WHATSAPP

            # Store the WhatsApp number in context
            formatted_number = number.replace(' ', '').replace('-', '').replace('(', '').replace(')', '')
            context.user_data['request_whatsapp'] = formatted_number
            self.logger.info(f"User {user_id} provided WhatsApp: {formatted_number}")

            # Ask for trading experience
            keyboard = [
                [InlineKeyboardButton("Beginner", callback_data="beginner_request")],
                [InlineKeyboardButton("Intermediate", callback_data="intermediate_request")],
                [InlineKeyboardButton("Pro", callback_data="pro_request")]
            ]

            await update.message.reply_text(
                "Please select your trading experience level:",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
            return WAITING_FOR_REQUEST_EXPERIENCE
        except Exception as e:
            self.logger.error(f"Error in handle_request_whatsapp: {str(e)}")
            traceback.print_exc()
            await update.message.reply_text("Sorry, there was an error processing your request. Please try again later.")
            return WAITING_FOR_CODE



    # Handlers for the verification request workflow

    async def handle_verification_request_name(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle name input for verification request"""
        try:
            user_id = update.message.from_user.id
            name = update.message.text.strip()

            self.logger.info(f"User {user_id} provided name for verification request: {name}")

            # Store the name in context
            context.user_data['verification_name'] = name

            # Ask for email
            await update.message.reply_text(
                "📧Your Email Address?",
                reply_markup=ForceReply(selective=True)
            )
            return WAITING_FOR_VERIFICATION_REQUEST_EMAIL
        except Exception as e:
            self.logger.error(f"Error in handle_verification_request_name: {str(e)}")
            traceback.print_exc()
            await update.message.reply_text("Sorry, there was an error processing your request. Please try again later.")
            return WAITING_FOR_CODE

    async def handle_verification_request_email(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle email input for verification request"""
        try:
            user_id = update.message.from_user.id
            email = update.message.text.strip()

            # Validate email format
            if not self.validate_email(email):
                await update.message.reply_text(
                    "⚠️ Invalid email format. Please enter a valid email address:",
                    reply_markup=ForceReply(selective=True)
                )
                return WAITING_FOR_VERIFICATION_REQUEST_EMAIL

            self.logger.info(f"User {user_id} provided email for verification request: {email}")

            # Store the email in context
            context.user_data['verification_email'] = email

            # Ask for WhatsApp number
            await update.message.reply_text(
                "📞Your Whatsapp number with country code (e.g., +91 9876543210) ?",
                reply_markup=ForceReply(selective=True)
            )
            return WAITING_FOR_VERIFICATION_REQUEST_WHATSAPP
        except Exception as e:
            self.logger.error(f"Error in handle_verification_request_email: {str(e)}")
            traceback.print_exc()
            await update.message.reply_text("Sorry, there was an error processing your request. Please try again later.")
            return WAITING_FOR_CODE

    async def handle_verification_request_whatsapp(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle WhatsApp number input for verification request"""
        try:
            user_id = update.message.from_user.id
            number = update.message.text.strip()

            # Validate WhatsApp number format
            if not self.validate_whatsapp(number):
                await update.message.reply_text(
                    "⚠️ Please enter a valid phone number with country code (e.g., +1XXXXXXXXXX for US, +44XXXXXXXXX for UK):",
                    reply_markup=ForceReply(selective=True)
                )
                return WAITING_FOR_VERIFICATION_REQUEST_WHATSAPP

            # Format and store the WhatsApp number
            formatted_number = number.replace(' ', '').replace('-', '').replace('(', '').replace(')', '')
            context.user_data['verification_whatsapp'] = formatted_number
            self.logger.info(f"User {user_id} provided WhatsApp for verification request: {formatted_number}")

            # Ask for trading experience
            keyboard = [
                [InlineKeyboardButton("Beginner", callback_data="beginner")],
                [InlineKeyboardButton("Intermediate", callback_data="intermediate")],
                [InlineKeyboardButton("Pro", callback_data="pro")]
            ]

            await update.message.reply_text(
                "Please select your trading experience level:",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
            return WAITING_FOR_VERIFICATION_REQUEST_EXPERIENCE
        except Exception as e:
            self.logger.error(f"Error in handle_verification_request_whatsapp: {str(e)}")
            traceback.print_exc()
            await update.message.reply_text("Sorry, there was an error processing your request. Please try again later.")
            return WAITING_FOR_CODE

    async def handle_verification_request_account(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle account number input for verification request"""
        try:
            user_id = update.message.from_user.id

            # Check if access_code is already in context (from verify_membership flow)
            if 'verification_access_code' in context.user_data:
                access_code = context.user_data['verification_access_code']
                self.logger.info(f"Using access code from context for user {user_id}: {access_code}")
            else:
                # Get access code from user input
                access_code = update.message.text.strip()
                self.logger.info(f"User {user_id} provided access code for verification request: {access_code}")
                # Store the access code in context
                context.user_data['verification_access_code'] = access_code

            # Prepare user details
            user_details = {
                'name': context.user_data.get('verification_name', 'Unknown'),
                'email': context.user_data.get('verification_email', 'Unknown'),
                'whatsapp': context.user_data.get('verification_whatsapp', 'Unknown'),
                'trading_experience': context.user_data.get('verification_trading_experience', 'Unknown')
            }
            self.logger.info(f"User details prepared with trading experience: {user_details['trading_experience']}")

            # Create verification request using the subscription manager
            try:
                # Get the bot ID
                telegram_bot_id = self.get_bot_id_safely(update)

                # Use the create_verification_request method which will check if the user needs verification
                # and if the access code is valid
                request_id, request_success = self.sub_manager.create_verification_request(user_id, access_code, user_details, telegram_bot_id=telegram_bot_id)

                if request_success:
                    if request_id == "AUTO-VERIFIED":
                        self.logger.info(f"User {user_id} was automatically verified with access code {access_code}")
                        success = True
                        auto_verified = True
                    else:
                        self.logger.info(f"Created verification request {request_id} for user {user_id}")
                        success = True
                        auto_verified = False
                else:
                    self.logger.error(f"Failed to create verification request for user {user_id}")
                    success = False
                    auto_verified = False

                # Also save to master_user_data if user doesn't exist there and not auto-verified
                if not auto_verified and not self.sub_manager.check_user_exists_in_master(user_id):
                    # Double-check that the access code is still not used by another user
                    # This helps prevent race conditions where two users try to use the same code simultaneously
                    existing_users = self.sub_manager.get_users_by_code(access_code)
                    self.logger.info(f"Existing users with access code {access_code}: {existing_users}")

                    # Check if there are multiple users with this access code
                    # OR if the current user is not in the list of existing users
                    code_used_by_other = len(existing_users) > 1 or (existing_users and str(user_id) not in [str(u) for u in existing_users])
                    self.logger.info(f"Code used by other check: {code_used_by_other}, existing users: {existing_users}")

                    if code_used_by_other:
                        # The code has been claimed by another user while this user was in the process
                        self.logger.warning(f"Access code {access_code} was claimed by another user while user {user_id} was being verified")

                        # Set success to False to show the appropriate message
                        success = False
                    else:
                        # If we get here, the code is still available for this user
                        master_user_data = {
                            'user_id': user_id,
                            'name': user_details['name'],
                            'email': user_details['email'],
                            'whatsapp': user_details['whatsapp'],
                            'trading_experience': user_details['trading_experience'],
                            'access_code': access_code,
                            'subscription': 'unknown',  # Set to unknown for verification requests
                            'user_verify': False,  # Default to not verified
                            'broker_reg': False,    # User doesn't have a valid broker registration yet
                            'user_status': 'verification_pending',  # Pending verification
                            'user_verify_status': 'pending',
                            'registration_time': datetime.now(timezone.utc),
                            'telegram_bot_id': self.get_bot_id_safely(update)  # Add the Telegram bot ID
                        }

                        # We don't need to check the return values here since we're just saving the data
                        self.sub_manager.save_to_master_user_data(master_user_data)
                        self.logger.info(f"Added user {user_id} to master_user_data collection")

                # If the user was automatically verified, send them the channel joining link
                if auto_verified:
                    channel_link = await self.create_one_time_invite_link(context)

                    message_text = f"Congratulations! Your profile is verified ✅ You are an exclusive Member With us 🥷"

                    keyboard = [
                        [InlineKeyboardButton("Access Premium space 🏆", url=channel_link)],
                        [InlineKeyboardButton("Explore my Benefits 🌍", callback_data="verify_explore_offerings")],
                        [InlineKeyboardButton("My Community Ranking 🏅", callback_data="community_ranking")],
                        [InlineKeyboardButton("Contact Withdrawals/Deposit Team 🔰", url="http://wa.me/+************")],
                        [InlineKeyboardButton("Need More Support?", callback_data="submit_request_start")],
                        [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
                    ]

                    await update.message.reply_text(
                        message_text,
                        reply_markup=InlineKeyboardMarkup(keyboard)
                    )

                    # Skip the regular confirmation message for auto-verified users
                    return WAITING_FOR_CODE
            except Exception as e:
                self.logger.error(f"Error creating verification request: {str(e)}")
                traceback.print_exc()
                success = False

            # Send confirmation message
            if success:
                message_text = "Your verification request has been submitted successfully ✅ Thank you for sharing your information, our team will review your account and get back to you soon 🙏"
            else:
                # Check if the access code is already used by another user
                existing_users = self.sub_manager.get_users_by_code(access_code)
                code_used_by_other = existing_users and str(user_id) not in [str(u) for u in existing_users]

                if code_used_by_other:
                    message_text = "⚠️ This access code is already associated with another user and cannot be used again. Please try a different access code or contact support for assistance.\n\nAccess code: " + access_code
                else:
                    message_text = "There was an issue submitting your verification request. Our team has been notified and will assist you shortly."

            keyboard = [
                [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
            ]

            await update.message.reply_text(
                message_text,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

            return WAITING_FOR_CODE
        except Exception as e:
            self.logger.error(f"Error in handle_verification_request_account: {str(e)}")
            traceback.print_exc()
            await update.message.reply_text("Sorry, there was an error processing your request. Please try again later.")
            return WAITING_FOR_CODE